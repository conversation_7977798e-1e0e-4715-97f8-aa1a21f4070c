from rest_framework.viewsets import ModelViewSet
from ..utils.response import APIResponse as Response
from ..models import UserPhoto, User3DModel
from ..serializers import UserPhotoSerializer, User3DModelSerializer


class UserPhotoViewSet(ModelViewSet):
    """
    用户照片视图集
    
    提供用户照片的增删改查功能
    """
    queryset = UserPhoto.objects.filter(deleted_at__isnull=True)
    serializer_class = UserPhotoSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='用户照片删除成功', http_status=204)


class User3DModelViewSet(ModelViewSet):
    """
    用户3D模型视图集
    
    提供用户3D模型的增删改查功能
    """
    queryset = User3DModel.objects.filter(deleted_at__isnull=True)
    serializer_class = User3DModelSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='用户3D模型删除成功', http_status=204)