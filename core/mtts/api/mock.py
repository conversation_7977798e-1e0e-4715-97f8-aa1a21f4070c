from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework import status
from django.conf import settings
import json
import os

def load_json_check(file_path):
    """
    工具函数：用以捕获异常，安全打开json测试文件
    """
    if not os.path.exists(file_path):
        return False, Response({
            "status": 1,
            "message": f"文件不存在：{file_path}"
        }, status=status.HTTP_400_BAD_REQUEST)

    if not os.access(file_path, os.R_OK):
        return False, Response({
            "status": 1,
            "message": f"无读取权限：{file_path}"
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return True, data
    
    except json.JSONDecodeError:
        return False, Response({
            "status": 1,
            "message": f"文件不是有效的JSON格式: {file_path}"
        }, status=status.HTTP_400_BAD_REQUEST)
    
    except Exception as e:
        return False, Response({
            "status": 1,
            "message": f"读取模拟数据文件出错：{str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class MockReceiveView(APIView):
    """
    模拟设备端将数据发送到后端
    如有post会优先使用请求数据
    """
    permission_classes = [AllowAny]

    def post(self, request):
        device_input = request.data  # 实际设备将通过 body 提交 JSON 格式数据
        # 如无post请求则使用默认模拟数据
        if not device_input:
            mock_path = os.path.join(settings.BASE_DIR, 'mtts', 'json', 'mock_receive.json')
            success, result = load_json_check(mock_path)
            if not success:
                return result
            device_input = result

        # 这里可以写保存、处理等逻辑
        # ...

        return Response({
            "status": 0,
            "message": "测试阶段：成功接收设备端发送的数据",
            "content": device_input
        }, status=status.HTTP_200_OK)


class MockSendView(APIView):
    """
    模拟将JSON数据发送到小程序端
    """
    permission_classes = [AllowAny]

    def get(self, request): 
        mock_path = os.path.join(settings.BASE_DIR, 'mtts', 'json', 'mock_send.json')
        success, result = load_json_check(mock_path)
        if not success:
            return result
        backend_send = result
        return Response([{
            "status": 0,
            "message": "success",
            "content": backend_send
        }], status=status.HTTP_200_OK)
