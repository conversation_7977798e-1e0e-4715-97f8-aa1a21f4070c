from rest_framework.viewsets import ModelViewSet
from ..utils.response import APIResponse as Response
from ..models import Org, UserOrg
from ..serializers import OrgSerializer, UserOrgSerializer


class OrgViewSet(ModelViewSet):
    """
    组织机构视图集
    
    提供组织机构的增删改查功能
    """
    queryset = Org.objects.filter(deleted_at__isnull=True)
    serializer_class = OrgSerializer

    def get_queryset(self):
        # 只返回当前用户所在机构的设备
        user = self.request.user
        return user.orgs.filter(deleted_at__isnull=True) if user.is_authenticated else Org.objects.none()
    
    def delete(self, request, *args, **kwargs):
        # 删除机构时，级联删除相关数据
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='机构删除成功', http_status=204)


class UserOrgViewSet(ModelViewSet):
    """
    用户-组织关联视图集
    
    提供用户-组织关联的增删改查功能
    """
    queryset = UserOrg.objects.all()  # UserOrg模型没有deleted_at字段
    serializer_class = UserOrgSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        # 直接物理删除，因为UserOrg模型没有软删除功能
        instance.delete()
        return Response(data_status=0, data_msg='用户-组织关联删除成功', http_status=204)