from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView, TokenVerifyView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
import requests
from django.conf import settings
from ..models import MiniAppUser, TestTaker
from ..serializers import MiniAppUserSerializer
from django.contrib.auth.models import User


class LoginView(TokenObtainPairView):
    """
    用户登录视图，返回JWT令牌
    """
    permission_classes = [AllowAny]


class CustomTokenObtainPairView(APIView):
    """
    自定义登录视图，可以返回更多用户信息
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')
        
        user = authenticate(username=username, password=password)
        
        if user:
            refresh = RefreshToken.for_user(user)
            
            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': {
                    'id': user.id,
                    'username': user.username,
                    # 可以添加更多用户信息
                }
            })
        
        return Response({"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED)


class MiniAppWeChatLoginView(APIView):
    """
    微信小程序登录/注册视图
    前端传 code，后端用 code 换 openid，自动注册并下发 JWT Token
    """
    permission_classes = [AllowAny]

    def post(self, request):
        code = request.data.get('code')
        if not code:
            return Response({'error': '缺少 code'}, status=status.HTTP_400_BAD_REQUEST)

        # 1. 用 code 换 openid 和 session_key
        appid = getattr(settings, 'WECHAT_MINIAPP_APPID', None)
        secret = getattr(settings, 'WECHAT_MINIAPP_SECRET', None)
        if not appid or not secret:
            return Response({'error': '未配置小程序 appid/secret'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        wx_url = f'https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={code}&grant_type=authorization_code'
        wx_resp = requests.get(wx_url)
        wx_data = wx_resp.json()
        openid = wx_data.get('openid')
        session_key = wx_data.get('session_key')
        unionid = wx_data.get('unionid')
        if not openid or not session_key:
            return Response({'error': '微信认证失败', 'detail': wx_data}, status=status.HTTP_400_BAD_REQUEST)

        # 2. 查找或创建 MiniAppUser
        miniapp_user, created = MiniAppUser.objects.get_or_create(openid=openid, defaults={
            'session_key': session_key,
            'unionid': unionid
        })
        if not created:
            miniapp_user.session_key = session_key
            if unionid:
                miniapp_user.unionid = unionid
            miniapp_user.save()

        # 3. 绑定 TestTaker（如有）
        # 可根据业务需求自动创建 TestTaker 或等待后续绑定

        # 4. 生成/获取 JWT Token
        # 若无 user，则为 openid 创建一个 user
        if not miniapp_user.test_taker or not miniapp_user.test_taker.user:
            # 自动创建 user 和 test_taker
            username = f"wx_{openid[:16]}"
            user, _ = User.objects.get_or_create(username=username, defaults={'password': User.objects.make_random_password()})
            test_taker, _ = TestTaker.objects.get_or_create(user=user, defaults={'name': username, 'gender': 'O', 'age': 0, 'height': 0})
            miniapp_user.test_taker = test_taker
            miniapp_user.save()
        else:
            user = miniapp_user.test_taker.user

        refresh = RefreshToken.for_user(user)
        data = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'miniapp_user': MiniAppUserSerializer(miniapp_user).data
        }
        return Response(data)