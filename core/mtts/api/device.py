from rest_framework.viewsets import ModelViewSet
from ..utils.response import APIResponse as Response
from ..models import Device, DeviceHeartbeat
from ..serializers import DeviceSerializer, DeviceHeartbeatSerializer


class DeviceViewSet(ModelViewSet):
    """
    设备视图集
    
    提供设备的增删改查功能
    """
    queryset = Device.objects.filter(deleted_at__isnull=True)
    serializer_class = DeviceSerializer

    def delete(self, request, *args, **kwargs):
        # 删除设备时，级联删除相关数据
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='设备删除成功', http_status=204)


class DeviceHeartbeatViewSet(ModelViewSet):
    """
    设备心跳记录视图集
    
    提供设备心跳记录的增删改查功能
    """
    queryset = DeviceHeartbeat.objects.filter(deleted_at__isnull=True)
    serializer_class = DeviceHeartbeatSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='设备心跳记录删除成功', http_status=204)