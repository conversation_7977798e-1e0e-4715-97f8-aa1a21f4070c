from rest_framework import serializers
from django.http import HttpResponse


def index(request):
    html_content = """
    <html>
    <head>
        <title>江苏福瑞 AI 服务器</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
            }
            h1 {
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
            }
            h2 {
                color: #2980b9;
                margin-top: 20px;
            }
            ul {
                padding-left: 20px;
            }
            li {
                margin-bottom: 10px;
            }
            a {
                color: #3498db;
                text-decoration: none;
                font-weight: bold;
            }
            a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>江苏福瑞 AI 服务器</h1>
            <p>欢迎访问江苏福瑞 AI 服务器。</p>
            
            <h2>如何访问 API 文档</h2>
            <p>您可以通过以下 URL 访问 API 文档：</p>
            <ul>
                <li><strong>Swagger UI:</strong> <a href="/swagger/">/swagger/</a></li>
                <li><strong>ReDoc:</strong> <a href="/redoc/">/redoc/</a></li>
                <li><strong>Admin:</strong> <a href="/admin/">/admin/</a></li>
            </ul>
            
            <p>这些文档页面将自动包含所有 API 端点，并显示相关的模型、参数和响应格式。管理员可以通过 Admin 页面登录管理系统。</p>
        </div>
    </body>
    </html>
    """
    return HttpResponse(html_content)