from rest_framework.viewsets import ModelViewSet
from ..utils.response import APIResponse as Response
from ..models import TestTaker, UserTestRecord, UserTestRecordDetail
from ..serializers import TestTakerSerializer, UserTestRecordSerializer, UserTestRecordDetailSerializer


class TestTakerViewSet(ModelViewSet):
    """
    体测用户视图集
    
    提供体测用户的增删改查功能
    """
    queryset = TestTaker.objects.filter(deleted_at__isnull=True)
    serializer_class = TestTakerSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='体测用户删除成功', http_status=204)


class UserTestRecordViewSet(ModelViewSet):
    """
    用户体测记录视图集
    
    提供用户体测记录的增删改查功能
    """
    queryset = UserTestRecord.objects.filter(deleted_at__isnull=True)
    serializer_class = UserTestRecordSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='用户体测记录删除成功', http_status=204)


class UserTestRecordDetailViewSet(ModelViewSet):
    """
    用户体测记录详情视图集
    
    提供用户体测记录详情的增删改查功能
    """
    queryset = UserTestRecordDetail.objects.filter(deleted_at__isnull=True)
    serializer_class = UserTestRecordDetailSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='用户体测记录详情删除成功', http_status=204)