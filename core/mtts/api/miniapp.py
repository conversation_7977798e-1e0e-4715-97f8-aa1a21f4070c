from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from mtts.models import MiniAppUser, TestTaker, UserTestRecord
from mtts.serializers import MiniAppUserSerializer, TestTakerSerializer, UserTestRecordSerializer
from django.contrib.auth.models import User

class MiniAppRegisterView(APIView):
    """
    小程序专用注册/补充体测者信息接口
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        # 绑定 openid 与体测者信息
        # 需要: name, gender, age, height
        user = request.user
        openid = request.data.get('openid')
        name = request.data.get('name')
        gender = request.data.get('gender')
        age = request.data.get('age')
        height = request.data.get('height')
        if not openid or not name or not gender or age is None or height is None:
            return Response({'error': '参数不完整'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            miniapp_user = MiniAppUser.objects.get(openid=openid, deleted_at__isnull=True)
        except MiniAppUser.DoesNotExist:
            return Response({'error': 'openid 未注册'}, status=status.HTTP_404_NOT_FOUND)
        # 更新/创建 TestTaker
        if miniapp_user.test_taker:
            test_taker = miniapp_user.test_taker
            test_taker.name = name
            test_taker.gender = gender
            test_taker.age = age
            test_taker.height = height
            test_taker.save()
        else:
            test_taker = TestTaker.objects.create(user=user, name=name, gender=gender, age=age, height=height)
            miniapp_user.test_taker = test_taker
            miniapp_user.save()
        return Response({'test_taker': TestTakerSerializer(test_taker).data})

class MiniAppTestSubmitView(APIView):
    """
    小程序体测数据上传接口
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        # 需要: openid, 测试数据（如 height, age, device, ...）
        openid = request.data.get('openid')
        test_data = request.data.get('test_data')
        if not openid or not test_data:
            return Response({'error': '参数不完整'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            miniapp_user = MiniAppUser.objects.get(openid=openid, deleted_at__isnull=True)
            test_taker = miniapp_user.test_taker
        except Exception:
            return Response({'error': 'openid 未注册或未绑定体测者'}, status=status.HTTP_404_NOT_FOUND)
        # 创建体测记录
        record = UserTestRecord.objects.create(user=test_taker, **test_data)
        return Response({'test_record': UserTestRecordSerializer(record).data})

class MiniAppReportView(APIView):
    """
    小程序体测报告查询接口
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # 支持通过 openid 或 test_taker id 查询
        openid = request.query_params.get('openid')
        test_taker_id = request.query_params.get('test_taker_id')
        if openid:
            try:
                miniapp_user = MiniAppUser.objects.get(openid=openid, deleted_at__isnull=True)
                test_taker = miniapp_user.test_taker
            except Exception:
                return Response({'error': 'openid 未注册或未绑定体测者'}, status=status.HTTP_404_NOT_FOUND)
        elif test_taker_id:
            try:
                test_taker = TestTaker.objects.get(id=test_taker_id, deleted_at__isnull=True)
            except Exception:
                return Response({'error': 'test_taker 不存在'}, status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({'error': '缺少 openid 或 test_taker_id'}, status=status.HTTP_400_BAD_REQUEST)
        # 查询体测记录
        records = UserTestRecord.objects.filter(user=test_taker, deleted_at__isnull=True).order_by('-test_date')
        return Response({'records': UserTestRecordSerializer(records, many=True).data}) 