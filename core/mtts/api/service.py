from rest_framework.viewsets import ModelViewSet
from ..utils.response import APIResponse as Response
from ..models import ServiceProvider
from ..serializers import ServiceProviderSerializer


class ServiceProviderViewSet(ModelViewSet):
    """
    服务商视图集
    
    提供服务商的增删改查功能
    """
    queryset = ServiceProvider.objects.filter(deleted_at__isnull=True)
    serializer_class = ServiceProviderSerializer

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response(data_status=0, data_msg='服务商删除成功', http_status=204)