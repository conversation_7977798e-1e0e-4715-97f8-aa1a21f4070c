# Generated by Django 5.2.3 on 2025-07-04 14:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mtts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MiniAppUser',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('openid', models.CharField(max_length=64, unique=True, verbose_name='微信OpenID')),
                ('unionid', models.CharField(blank=True, max_length=64, null=True, verbose_name='微信UnionID')),
                ('session_key', models.CharField(blank=True, max_length=128, null=True, verbose_name='Session Key')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True, verbose_name='删除时间')),
                ('test_taker', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mtts.testtaker', verbose_name='关联体测者')),
            ],
            options={
                'verbose_name': '微信小程序用户',
                'verbose_name_plural': '微信小程序用户',
            },
        ),
    ]
