# Generated by Django 5.2.3 on 2025-07-03 20:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceHeartbeat',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('device', models.CharField(max_length=50, verbose_name='设备ID')),
                ('heartbeat_time', models.DateTimeField(auto_now_add=True, verbose_name='心跳时间')),
                ('status', models.CharField(choices=[('online', '在线'), ('offline', '离线'), ('error', '异常')], default='online', max_length=20, verbose_name='心跳状态')),
                ('gps', models.CharField(blank=True, max_length=100, null=True, verbose_name='GPS坐标')),
                ('location', models.CharField(blank=True, max_length=200, null=True, verbose_name='位置描述')),
                ('cpu_usage', models.FloatField(blank=True, null=True, verbose_name='CPU使用率(%)')),
                ('memory_usage', models.FloatField(blank=True, null=True, verbose_name='内存使用率(%)')),
                ('temperature', models.FloatField(blank=True, null=True, verbose_name='设备温度(℃)')),
                ('error_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='错误代码')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
            ],
            options={
                'verbose_name': '设备心跳记录',
                'verbose_name_plural': '设备心跳记录',
                'ordering': ['-heartbeat_time'],
            },
        ),
        migrations.CreateModel(
            name='User3DModel',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模型名称')),
                ('user_test_record', models.CharField(max_length=200, verbose_name='关联测试记录')),
                ('model_type', models.CharField(choices=[('obj', 'OBJ格式'), ('stl', 'STL格式'), ('fbx', 'FBX格式'), ('gltf', 'glTF格式'), ('other', '其他格式')], default='obj', max_length=10, verbose_name='模型格式')),
                ('model_url', models.URLField(verbose_name='模型文件URL')),
                ('model_date_time', models.DateTimeField(auto_now_add=True, verbose_name='模型生成时间')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
            ],
            options={
                'verbose_name': '体测者记录表用户3D模型',
                'verbose_name_plural': '体测者记录表用户3D模型',
                'ordering': ['-model_date_time'],
            },
        ),
        migrations.CreateModel(
            name='UserPhoto',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='照片名称')),
                ('user_test_record', models.CharField(max_length=200, verbose_name='关联测试记录')),
                ('photo_index', models.PositiveSmallIntegerField(verbose_name='照片序号')),
                ('photo_date_time', models.DateTimeField(auto_now_add=True, verbose_name='拍摄时间')),
                ('photo_url', models.URLField(verbose_name='照片URL')),
                ('photo_data', models.BinaryField(blank=True, null=True, verbose_name='照片二进制数据')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
            ],
            options={
                'verbose_name': '体测者记录表用户照片',
                'verbose_name_plural': '体测者记录表用户照片',
            },
        ),
        migrations.CreateModel(
            name='Org',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='机构名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='机构代码')),
                ('kind', models.CharField(choices=[('client', '客户'), ('service', '服务商')], max_length=10, verbose_name='机构类型')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='mtts.org', verbose_name='上级机构')),
            ],
            options={
                'verbose_name': '组织机构',
                'verbose_name_plural': '组织机构',
            },
        ),
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.CharField(max_length=50, primary_key=True, serialize=False, verbose_name='设备ID')),
                ('name', models.CharField(max_length=100, verbose_name='设备名称')),
                ('online_time', models.DateTimeField(blank=True, null=True, verbose_name='上线时间')),
                ('offline_time', models.DateTimeField(blank=True, null=True, verbose_name='离线时间')),
                ('status', models.CharField(choices=[('online', '在线'), ('offline', '离线'), ('flash', '全新'), ('repair', '维修中'), ('broken', '故障'), ('scrap', '报废'), ('maintenance', '维护中')], default='flash', max_length=20, verbose_name='状态')),
                ('last_gps', models.CharField(blank=True, max_length=100, null=True, verbose_name='最后GPS位置')),
                ('last_location', models.CharField(blank=True, max_length=200, null=True, verbose_name='最后位置描述')),
                ('device_version', models.CharField(blank=True, max_length=50, null=True, verbose_name='设备版本')),
                ('device_type', models.CharField(choices=[('aidevice', '3D体态仪设备'), ('other', '其他设备')], default='aidevice', max_length=20, verbose_name='设备类型')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
                ('org', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='mtts.org', verbose_name='所属组织')),
            ],
            options={
                'verbose_name': '设备',
                'verbose_name_plural': '设备',
            },
        ),
        migrations.CreateModel(
            name='ServiceProvider',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='服务商代码')),
                ('name', models.CharField(max_length=100, verbose_name='服务商名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='服务商描述')),
                ('address', models.CharField(blank=True, max_length=255, null=True, verbose_name='服务商地址')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('contact_person', models.CharField(max_length=100, verbose_name='联系人')),
                ('contact_phone', models.CharField(max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='电子邮箱')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
                ('org', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mtts.org', verbose_name='所属组织')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '服务商',
                'verbose_name_plural': '服务商',
            },
        ),
        migrations.CreateModel(
            name='TestTaker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='姓名')),
                ('gender', models.CharField(choices=[('M', '男'), ('F', '女'), ('O', '其他')], max_length=1, verbose_name='性别')),
                ('age', models.PositiveSmallIntegerField(verbose_name='年龄')),
                ('height', models.FloatField(verbose_name='身高(cm)')),
                ('last_test_date', models.DateField(blank=True, null=True, verbose_name='最后测试日期')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
                ('last_test_device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mtts.device', verbose_name='最后测试设备')),
                ('last_test_org', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mtts.org', verbose_name='最后测试机构')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '体测者',
                'verbose_name_plural': '体测者',
            },
        ),
        migrations.CreateModel(
            name='UserTestRecord',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='记录名称')),
                ('gender', models.CharField(choices=[('M', '男'), ('F', '女'), ('O', '其他')], max_length=1, verbose_name='性别')),
                ('age', models.PositiveSmallIntegerField(verbose_name='年龄')),
                ('height', models.FloatField(verbose_name='身高(cm)')),
                ('test_date', models.DateTimeField(auto_now_add=True, verbose_name='测试时间')),
                ('test_gps', models.CharField(blank=True, max_length=100, null=True, verbose_name='测试位置(GPS)')),
                ('report_url', models.URLField(blank=True, null=True, verbose_name='报告URL')),
                ('report_createtime', models.DateTimeField(blank=True, null=True, verbose_name='报告生成时间')),
                ('report_status', models.CharField(choices=[('pending', '待生成'), ('generating', '生成中'), ('completed', '已完成'), ('failed', '生成失败')], default='pending', max_length=20, verbose_name='报告状态')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
                ('device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mtts.device', verbose_name='测试设备')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mtts.testtaker', verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '体测者记录表',
                'verbose_name_plural': '体测者记录表',
            },
        ),
        migrations.CreateModel(
            name='UserTestRecordDetail',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=100, null=True, verbose_name='详情名称')),
                ('user_test_record', models.CharField(max_length=200, verbose_name='关联测试记录')),
                ('content', models.TextField(blank=True, null=True, verbose_name='检测数据')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('modify_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人')),
                ('modify_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('deleted_at', models.DateTimeField(db_index=True, null=True)),
                ('device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mtts.device', verbose_name='测试设备')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mtts.testtaker', verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '体测者记录表测试记录详情',
                'verbose_name_plural': '体测者记录表测试记录详情',
            },
        ),
        migrations.CreateModel(
            name='UserOrg',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('org', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mtts.org', verbose_name='所属组织')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '组织机构用户关系表',
                'verbose_name_plural': '组织机构用户关系表',
                'unique_together': {('user', 'org')},
            },
        ),
    ]
