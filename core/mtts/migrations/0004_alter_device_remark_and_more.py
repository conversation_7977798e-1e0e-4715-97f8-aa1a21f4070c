# Generated by Django 5.2.3 on 2025-07-04 19:38

import ckeditor.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('mtts', '0003_miniappuser_create_user_miniappuser_modify_user_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='device',
            name='remark',
            field=ckeditor.fields.RichTextField(blank=True, null=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='deviceheartbeat',
            name='error_message',
            field=ckeditor.fields.RichTextField(blank=True, null=True, verbose_name='错误信息'),
        ),
        migrations.AlterField(
            model_name='deviceheartbeat',
            name='remark',
            field=ckeditor.fields.RichTextField(blank=True, null=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='usertestrecord',
            name='remark',
            field=ckeditor.fields.RichTextField(blank=True, null=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='usertestrecorddetail',
            name='content',
            field=ckeditor.fields.RichTextField(blank=True, null=True, verbose_name='检测数据'),
        ),
        migrations.AlterField(
            model_name='usertestrecorddetail',
            name='remark',
            field=ckeditor.fields.RichTextField(blank=True, null=True, verbose_name='备注'),
        ),
    ]
