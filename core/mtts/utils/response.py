from rest_framework.response import Response


class APIResponse(Response):
    def __init__(self, data_status=0, data_msg='ok', results=None, http_status=None, headers=None, exception=False,
                 **kwargs):
        # data 的初始状态：状态码与状态信息
        data = {
            'status': data_status,
            'msg': data_msg,
        }
        # data 的响应数据体
        if results is not None:
            data['results'] = results
        # data 响应的其他内容
        data.update(kwargs)
        super().__init__(data=data, status=http_status, headers=headers,
                 exception=exception)
