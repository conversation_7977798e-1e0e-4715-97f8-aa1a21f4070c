from django.contrib import admin
from .models import *

# Register your models here.
admin.site.site_header = '福瑞健康云平台'
admin.site.site_title = '福瑞健康云平台'
admin.site.index_title = 'Welcome to 福瑞健康云平台'


# OrgAdmin
class OrgAdmin(admin.ModelAdmin):
    fields = ['name', 'code', 'kind', 'status']
    list_display = ('id', 'code','parent', 'name', 'kind', 'status')
    search_fields = ['name']
    list_editable = ['name', 'code', 'kind', 'status']
    list_filter = ['kind', 'status']
    
    list_per_page = 20  # 每页显示20条记录

from mtts.utils import snowflake

class DeviceAdmin(admin.ModelAdmin):
    list_display = ['id', 'device_type', 'name', 'status', 'last_location', 'org','online_time']
    search_fields = ['name', 'org','device_type']
    list_filter = ['device_type','org', 'status', 'org']
    # list_editable = ['name', 'status']
    list_per_page = 20
    readonly_fields = ['id']  # 将ID字段设为只读

    actions = ['custom_button']

    def save_model(self, request, obj, form, change):
        """保存模型时自动生成雪花ID"""
        if not change:  # 如果是新建而不是修改
            obj.id = str(snowflake.next_id())
        super().save_model(request, obj, form, change)

    def custom_button(self, request, queryset):
        print(queryset)

    custom_button.confirm = '你是否执意要点击这个按钮？'
    custom_button.short_description = '自定义按钮'
    custom_button.type = 'danger'

class UserOrgAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'org']
    search_fields = ['user__username', 'org__name']
    list_filter = ['org']
    list_per_page = 20

class DeviceHeartbeatAdmin(admin.ModelAdmin):
    list_display = ['id', 'device', 'heartbeat_time', 'status', 'location', 'cpu_usage', 'memory_usage', 'temperature']
    search_fields = ['device']
    list_filter = ['status', 'heartbeat_time']
    list_per_page = 20

class TestTakerAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'name', 'gender', 'age', 'height', 'last_test_date', 'last_test_device', 'last_test_org']
    search_fields = ['name', 'user__username']
    list_filter = ['gender', 'last_test_org']
    list_editable = ['name', 'gender', 'age', 'height']
    list_per_page = 20

class UserTestRecordAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'user', 'device', 'gender', 'age', 'height', 'test_date', 'report_status', 'status']
    search_fields = ['name', 'user__name']
    list_filter = ['gender', 'report_status', 'status', 'device']
    list_editable = ['name', 'status']
    list_per_page = 20

class UserTestRecordDetailAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'user_test_record', 'user', 'device', 'create_time']
    search_fields = ['name', 'user__name', 'user_test_record']
    list_filter = ['device', 'create_time']
    list_editable = ['name']
    list_per_page = 20

class UserPhotoAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'user_test_record', 'photo_index', 'photo_date_time', 'photo_url']
    search_fields = ['name', 'user_test_record']
    list_filter = ['photo_date_time']
    list_editable = ['name']
    list_per_page = 20

class User3DModelAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'user_test_record', 'model_type', 'model_url', 'model_date_time']
    search_fields = ['name', 'user_test_record']
    list_filter = ['model_type', 'model_date_time']
    list_editable = ['name', 'model_type']
    list_per_page = 20

class ServiceProviderAdmin(admin.ModelAdmin):
    list_display = ['id', 'code', 'name', 'user', 'org', 'contact_person', 'contact_phone', 'status']
    search_fields = ['name', 'code', 'contact_person']
    list_filter = ['org', 'status']
    list_editable = ['name', 'code', 'status']
    list_per_page = 20

class MiniAppUserAdmin(admin.ModelAdmin):
    list_display = ['id', 'openid', 'unionid', 'test_taker', 'create_time', 'modify_time', 'deleted_at']
    search_fields = ['openid', 'unionid']
    list_filter = ['create_time', 'modify_time', 'deleted_at']
    list_per_page = 20
    readonly_fields = ['id', 'create_time', 'modify_time', 'deleted_at']

admin.site.register(Org, OrgAdmin)
admin.site.register(UserOrg, UserOrgAdmin)
admin.site.register(Device, DeviceAdmin)
admin.site.register(DeviceHeartbeat, DeviceHeartbeatAdmin)

admin.site.register(TestTaker, TestTakerAdmin)
admin.site.register(UserTestRecord, UserTestRecordAdmin)
admin.site.register(UserTestRecordDetail, UserTestRecordDetailAdmin)
admin.site.register(UserPhoto, UserPhotoAdmin)
admin.site.register(User3DModel, User3DModelAdmin)
admin.site.register(ServiceProvider, ServiceProviderAdmin)
admin.site.register(MiniAppUser, MiniAppUserAdmin)

# 这两个模型在models.py中已被注释掉，所以这里也需要注释掉
# admin.site.register(ReportIndicator)
# admin.site.register(ReportIndicatorRecord)