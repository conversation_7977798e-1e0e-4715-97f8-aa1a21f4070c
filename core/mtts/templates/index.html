<!DOCTYPE html>
<html>

<head>
  <title>江苏福瑞AI数据管理系统</title>
  <style>
    .card-link {
      text-decoration: none;
      color: inherit;
    }

    .card-link:active .card {
      transform: translateY(0);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .cards-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 20px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .card-icon {
      font-size: 2rem;
      margin-bottom: 15px;
      color: #2c3e50;
    }

    .card-title {
      font-size: 1.2rem;
      margin-bottom: 10px;
      color: #2c3e50;
    }

    .card-desc {
      color: #7f8c8d;
      font-size: 0.9rem;
    }

    @media (max-width: 768px) {
      .cards-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      }
    }
  </style>
</head>

<body>
  <div class="cards-container">
    <a href="/admin" class="card-link">
      <div class="card">
        <div class="card-icon">📊</div>
        <h3 class="card-title">数据管理</h3>
        <p class="card-desc">管理系统核心数据</p>
      </div>
    </a>
    <div class="card">
      <div class="card-icon">👥</div>
      <h3 class="card-title">用户管理</h3>
      <p class="card-desc">管理系统用户和权限</p>
    </div>
    <div class="card">
      <div class="card-icon">⚙️</div>
      <h3 class="card-title">系统设置</h3>
      <p class="card-desc">配置系统参数</p>
    </div>
    <a href="/redoc" class="card-link">
      <div class="card">
        <div class="card-icon">📚</div>
        <h3 class="card-title">文档中心</h3>
        <p class="card-desc">查看系统文档</p>
      </div>
    </a>
    <div class="card">
      <div class="card-icon">📈</div>
      <h3 class="card-title">报表统计</h3>
      <p class="card-desc">查看数据分析报表</p>
    </div>
    <div class="card">
      <div class="card-icon">❓</div>
      <h3 class="card-title">帮助中心</h3>
      <p class="card-desc">获取使用帮助</p>
    </div>
  </div>
</body>

</html>