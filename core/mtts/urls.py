from django.urls import re_path, include, path
from rest_framework import routers
from .views import home
from .api.master import index
from .api.mock import MockReceiveView, MockSendView
from .api.login import LoginView, CustomTokenObtainPairView, MiniAppWeChatLoginView
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView
from .api.miniapp import MiniAppRegisterView, MiniAppTestSubmitView, MiniAppReportView
from .api import (
    DeviceViewSet,
    DeviceHeartbeatViewSet,
    OrgViewSet,
    UserOrgViewSet,
    TestTakerViewSet,
    UserTestRecordViewSet,
    UserTestRecordDetailViewSet,
    UserPhotoViewSet,
    User3DModelViewSet,
    ServiceProviderViewSet,
)

router = routers.DefaultRouter()

# 设备相关路由
router.register(r'devices', DeviceViewSet)
router.register(r'device-heartbeats', DeviceHeartbeatViewSet)

# 组织机构相关路由
router.register(r'orgs', OrgViewSet)
router.register(r'user-orgs', UserOrgViewSet)

# 体测相关路由
router.register(r'user-tests', TestTakerViewSet)
router.register(r'user-test-records', UserTestRecordViewSet)
router.register(r'user-test-record-details', UserTestRecordDetailViewSet)

# 媒体数据路由
router.register(r'user-photos', UserPhotoViewSet)
router.register(r'user-3dmodels', User3DModelViewSet)

# 服务商路由
router.register(r'service-providers', ServiceProviderViewSet)

urlpatterns = [    
    path('', home, name='home'),
    re_path(r'^api/v1/', include(router.urls)),
    
    # JWT认证相关路由
    path('api/v1/token/', LoginView.as_view(), name='token_obtain_pair'),
    path('api/v1/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/v1/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('api/v1/login/', CustomTokenObtainPairView.as_view(), name='custom_login'),

    # 微信小程序登录路由
    path('api/miniapp/login/', MiniAppWeChatLoginView.as_view(), name='miniapp_wechat_login'),
    path('api/miniapp/register/', MiniAppRegisterView.as_view(), name='miniapp_register'),
    path('api/miniapp/test/submit/', MiniAppTestSubmitView.as_view(), name='miniapp_test_submit'),
    path('api/miniapp/report/', MiniAppReportView.as_view(), name='miniapp_report'),

    # 前后端JSON模拟数据
    path('api/mock/receive', MockReceiveView.as_view(), name='mock_receive'),
    path('api/mock/send', MockSendView.as_view(), name='mock_send'),
]