from django.db import models
from ckeditor.fields import RichTextField
from django.utils import timezone




# Create your models here.

class Org(models.Model):
    """
    组织机构模型
    """
    id = models.AutoField(primary_key=True)  # 自增主键ID
    name = models.CharField(max_length=100, verbose_name='机构名称')  # 机构名称
    code = models.CharField(max_length=50, unique=True, verbose_name='机构代码')  # 唯一机构代码
    
    # 机构类型选项
    KIND_CHOICES = [
        ('client', '客户'),
        ('service', '服务商')
    ]
    kind = models.CharField(max_length=10, choices=KIND_CHOICES, verbose_name='机构类型')  # 机构类型
    
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='上级机构')  # 上级机构(自引用)
    status = models.BooleanField(default=True, verbose_name='状态')  # 启用/禁用状态

    create_user = models.Char<PERSON>ield(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')



    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name = '组织机构'
        verbose_name_plural = '组织机构'

    def __str__(self):
        """
        返回机构名称作为字符串表示
        """
        return self.name

from mtts.utils import snowflake

# 雪花算法生成设备ID
class Device(models.Model):
    """
    设备模型
    """
    id = models.CharField(max_length=50, primary_key=True, verbose_name='设备ID')  # 设备唯一标识
    name = models.CharField(max_length=100, verbose_name='设备名称')  # 设备名称
    online_time = models.DateTimeField(null=True, blank=True, verbose_name='上线时间')  # 上线时间
    offline_time = models.DateTimeField(null=True, blank=True, verbose_name='离线时间')  # 离线时间
    
    WEIGHT_UNIT_CHOICES = [
        ('kg', '公斤'),
        ('jin', '斤')
    ]
    weight_unit = models.CharField(max_length=20, choices=WEIGHT_UNIT_CHOICES, default='kg', verbose_name='重量单位')  # 重量单位

    # 设备状态选项
    STATUS_CHOICES = [
        ('online', '在线'),
        ('offline', '离线'),
        ('flash', '全新'),
        ('repair', '维修中'),
        ('broken', '故障'),
        ('scrap', '报废'),
        ('maintenance', '维护中')
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='flash', verbose_name='状态')  # 设备状态
    
    last_gps = models.CharField(max_length=100, null=True, blank=True, verbose_name='最后GPS位置')  # 最后GPS坐标
    #- 使用CharField存储经纬度字符串（如"31.2304,121.4737"）- - 简单易用但缺乏地理查询功能

    last_location = models.CharField(max_length=200, null=True, blank=True, verbose_name='最后位置描述')  # 最后位置描述
    device_version = models.CharField(max_length=50, null=True, blank=True, verbose_name='设备版本')  # 设备版本号
    
    org = models.ForeignKey(Org, on_delete=models.DO_NOTHING, verbose_name='所属组织')  # 设备归属组织
    
    DEVICE_TYPE_CHOICES = [
        ('aidevice', '3D体态仪设备'),
        ('other', '其他设备')
    ]
    device_type = models.CharField(max_length=20, choices=DEVICE_TYPE_CHOICES, default='aidevice', verbose_name='设备类型')  # 设备类型
    remark = RichTextField(null=True, blank=True, verbose_name='备注')  # 备注信息

    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')


    
    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    def save(self, *args, **kwargs):
        """重写save方法，在创建新设备时自动生成雪花ID"""        
        if not self.id:
            # 新创建的设备，生成雪花ID
            self.id = str(snowflake.next_id())
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = '设备'
        verbose_name_plural = '设备'
    def __str__(self):
        """
        返回设备名称作为字符串表示
        """
        return self.name

class DeviceHeartbeat(models.Model):
    """
    设备心跳记录模型
    """
    id = models.AutoField(primary_key=True, verbose_name='ID')  # 自增主键
    device = models.ForeignKey(Device, on_delete=models.CASCADE, null=True, blank=True, verbose_name='设备ID')
    heartbeat_time = models.DateTimeField(auto_now_add=True, verbose_name='心跳时间')  # 心跳时间
    
    STATUS_CHOICES = [
        ('online', '在线'),
        ('offline', '离线'),
        ('error', '异常')
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='online', verbose_name='心跳状态')  # 心跳状态
    
    gps = models.CharField(max_length=100, null=True, blank=True, verbose_name='GPS坐标')  # GPS坐标
    location = models.CharField(max_length=200, null=True, blank=True, verbose_name='位置描述')  # 位置描述
    
    # 设备运行信息
    cpu_usage = models.FloatField(null=True, blank=True, verbose_name='CPU使用率(%)')  
    memory_usage = models.FloatField(null=True, blank=True, verbose_name='内存使用率(%)')
    temperature = models.FloatField(null=True, blank=True, verbose_name='设备温度(℃)')
    
    error_code = models.CharField(max_length=50, null=True, blank=True, verbose_name='错误代码')  # 错误代码
    error_message = RichTextField(null=True, blank=True, verbose_name='错误信息')  # 错误信息
    remark = RichTextField(null=True, blank=True, verbose_name='备注')  # 备注信息

    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name = '设备心跳记录'
        verbose_name_plural = '设备心跳记录'
        ordering = ['-heartbeat_time']  # 按心跳时间倒序

    def __str__(self):
        """
        返回设备心跳记录的字符串表示
        """
        return f"{self.device.name} - {self.heartbeat_time}"


class TestTaker(models.Model):
    """
    体测者模型
    """
    GENDER_CHOICES = [
        ('M', '男'),
        ('F', '女'),
        ('O', '其他')
    ]
    
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE, verbose_name='关联用户')
    name = models.CharField(max_length=100, verbose_name='姓名')
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, verbose_name='性别')
    age = models.PositiveSmallIntegerField(verbose_name='年龄')
    height = models.FloatField(verbose_name='身高(cm)')
    last_test_date = models.DateField(null=True, blank=True, verbose_name='最后测试日期')
    last_test_device = models.ForeignKey(Device, null=True, blank=True, on_delete=models.SET_NULL, verbose_name='最后测试设备')
    last_test_org = models.ForeignKey(Org, null=True, blank=True, on_delete=models.SET_NULL, verbose_name='最后测试机构')

    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name = '体测者'
        verbose_name_plural = '体测者'

    def __str__(self):
        """
        返回体测者的字符串表示
        """
        return f"{self.name} ({self.get_gender_display()})"


class UserTestRecord(models.Model):
    """
    测试记录表
    """
    REPORT_STATUS_CHOICES = [
        ('pending', '待生成'),
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '生成失败')
    ]
    
    id = models.AutoField(primary_key=True, verbose_name='ID')
    name = models.CharField(max_length=100, verbose_name='记录名称')
    user = models.ForeignKey(TestTaker, on_delete=models.SET_NULL, verbose_name='关联用户', null=True, blank=True)
    device = models.ForeignKey(Device, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='测试设备')
    gender = models.CharField(max_length=1, choices=TestTaker.GENDER_CHOICES, verbose_name='性别')
    age = models.PositiveSmallIntegerField(verbose_name='年龄')
    height = models.FloatField(verbose_name='身高(cm)')
    test_date = models.DateTimeField(auto_now_add=True, verbose_name='测试时间')
    test_gps = models.CharField(max_length=100, null=True, blank=True, verbose_name='测试位置(GPS)')
    json_content = models.JSONField(null=True, blank=True, verbose_name='测试内容(JSON)')
    report_url = models.URLField(null=True, blank=True, verbose_name='报告URL')
    report_createtime = models.DateTimeField(null=True, blank=True, verbose_name='报告生成时间')
    report_status = models.CharField(
        max_length=20, 
        choices=REPORT_STATUS_CHOICES, 
        default='pending', 
        verbose_name='报告状态'
    )
    remark = RichTextField(null=True, blank=True, verbose_name='备注')
    status = models.BooleanField(default=True, verbose_name='状态')

    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        """
        定义数据库表结构
        """
        verbose_name = '体测者记录表'
        verbose_name_plural = verbose_name    
    def __str__(self):
        """
        返回测试记录的字符串表示
        """
        return f"{self.user.username}的测试记录({self.test_date})"


class UserPhoto(models.Model):
    """
    测试照片存储模型
    """
    id = models.AutoField(primary_key=True, verbose_name='ID')
    name = models.CharField(max_length=100, verbose_name='照片名称')
    user_test_record = models.ForeignKey(UserTestRecord, on_delete=models.CASCADE, null=True, blank=True, verbose_name='关联测试记录')
    photo_index = models.PositiveSmallIntegerField(verbose_name='照片序号')
    photo_date_time = models.DateTimeField(auto_now_add=True, verbose_name='拍摄时间')
    photo_url = models.URLField(verbose_name='照片URL')
    photo_data = models.BinaryField(null=True, blank=True, verbose_name='照片二进制数据')
    remark = models.TextField(null=True, blank=True, verbose_name='备注')
  

    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()
    class Meta:
        """
        元数据
        """
        verbose_name = '体测者记录表用户照片'
        verbose_name_plural = '体测者记录表用户照片'
        

    def __str__(self):
        """
        返回照片的字符串表示
        """
        return f"{self.user_test}的照片#{self.photo_index}"


class User3DModel(models.Model):
    """
    用户3D模型文件存储
    """
    MODEL_TYPE_CHOICES = [
        ('obj', 'OBJ格式'),
        ('stl', 'STL格式'),
        ('fbx', 'FBX格式'),
        ('gltf', 'glTF格式'),
        ('other', '其他格式')
    ]
    
    id = models.AutoField(primary_key=True, verbose_name='ID')
    name = models.CharField(max_length=100, verbose_name='模型名称')
    user_test_record = models.ForeignKey(UserTestRecord, on_delete=models.CASCADE, null=True, blank=True, verbose_name='关联测试记录')
    model_type = models.CharField(
        max_length=10, 
        choices=MODEL_TYPE_CHOICES, 
        default='obj',
        verbose_name='模型格式'
    )
    model_url = models.URLField(verbose_name='模型文件URL')
    model_date_time = models.DateTimeField(auto_now_add=True, verbose_name='模型生成时间')

    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

    
    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name = '体测者记录表用户3D模型'
        verbose_name_plural = '体测者记录表用户3D模型'
        ordering = ['-model_date_time']

    def __str__(self):
        """
        返回3D模型的字符串表示
        """
        return f"{self.user_test}的{self.get_model_type_display()}模型"


class UserTestRecordDetail(models.Model):
    """
    测试记录详情表
    """
    id = models.AutoField(primary_key=True, verbose_name='ID')
    name = models.CharField(max_length=100, verbose_name='详情名称',null=True, blank=True)
    user_test_record = models.ForeignKey(UserTestRecord, on_delete=models.CASCADE, null=True, blank=True, verbose_name='关联测试记录')
    user = models.ForeignKey(TestTaker, on_delete=models.SET_NULL, verbose_name='关联用户',null=True, blank=True)
    device = models.ForeignKey(Device, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='测试设备')

    # 设备端测量的基础数据
    height = models.FloatField(null=True, blank=True, verbose_name='身高')
    weight = models.FloatField(null=True, blank=True, verbose_name='体重')
    chest = models.FloatField(null=True, blank=True, verbose_name='胸围')
    waist = models.FloatField(null=True, blank=True, verbose_name='腰围')
    hip = models.FloatField(null=True, blank=True, verbose_name='臀围')
    
    # 计算数据
    body_fat_rate = models.FloatField(null=True, blank=True, verbose_name='体脂率')
    bmi = models.FloatField(null=True, blank=True, verbose_name='BMI')
    gravity_left_front = models.FloatField(null=True, blank=True, verbose_name='重心：左前')
    gravity_right_front = models.FloatField(null=True, blank=True, verbose_name='重心：右前')
    gravity_left_back = models.FloatField(null=True, blank=True, verbose_name='重心：左后')
    gravity_right_back = models.FloatField(null=True, blank=True, verbose_name='重心：右后')
    weight_goal = models.FloatField(null=True, blank=True, verbose_name='目标体重')
    weight_control = models.FloatField(null=True, blank=True, verbose_name='体重控制')
    axunge_control = models.FloatField(null=True, blank=True, verbose_name='脂肪控制')
    muscle_weight = models.FloatField(null=True, blank=True, verbose_name='肌肉重量')
    metabolism = models.FloatField(null=True, blank=True, verbose_name='基础代谢')
    age_body = models.PositiveSmallIntegerField(verbose_name='身体年龄')

    # 复杂数据/评估结果
    muscle_control = models.CharField(max_length=100, null=True, blank=True, verbose_name='肌肉控制')
    head_neck_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='头颈形态')
    head_lateral_tilt = models.CharField(max_length=100, null=True, blank=True, verbose_name='头侧倾')
    shoulder_back_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='肩背形态')
    shoulder_slope = models.CharField(max_length=100, null=True, blank=True, verbose_name='肩斜度')
    uneven_shoulder = models.CharField(max_length=100, null=True, blank=True, verbose_name='高低肩')
    back_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='背部')
    waist_abdomen_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='腰腹形态')
    pelvis_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='骨盆形态')
    hip_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='臀部形态')
    long_short_leg = models.CharField(max_length=100, null=True, blank=True, verbose_name='长短腿')
    x_o_leg = models.CharField(max_length=100, null=True, blank=True, verbose_name='XO腿')
    knee_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='膝盖形态')
    calf_morphology = models.CharField(max_length=100, null=True, blank=True, verbose_name='小腿形态')
    cervical_spine_deviation = models.CharField(max_length=100, null=True, blank=True, verbose_name='颈椎偏移')
    shoulder_balance = models.CharField(max_length=100, null=True, blank=True, verbose_name='肩部平衡')
    scapula_rotation_balance = models.CharField(max_length=100, null=True, blank=True, verbose_name='肩胛骨外旋平衡')
    winged_scapula = models.CharField(max_length=100, null=True, blank=True, verbose_name='翼状肩胛骨')
    pelvis_balance = models.CharField(max_length=100, null=True, blank=True, verbose_name='骨盆平衡')
    gravity_left_and_right = models.CharField(max_length=100, null=True, blank=True, verbose_name='身体左右重心')
    gravity_front_and_back = models.CharField(max_length=100, null=True, blank=True, verbose_name='身体前后重心')
    cervical_curvature = models.CharField(max_length=100, null=True, blank=True, verbose_name='颈椎曲度')
    thoracic_curvature = models.CharField(max_length=100, null=True, blank=True, verbose_name='胸椎曲度')
    lumbar_curvature = models.CharField(max_length=100, null=True, blank=True, verbose_name='腰椎曲度')
    pelvis_tilt = models.CharField(max_length=100, null=True, blank=True, verbose_name='骨盆倾斜')

    remark = RichTextField(null=True, blank=True, verbose_name='备注')

    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')
    
    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name = '体测者记录表测试记录详情'
        verbose_name_plural = '体测者记录表测试记录详情'

    def __str__(self):
        """
        返回测试记录详情的字符串表示
        """
        return f"{self.user.username}的测试记录详情"






class UserOrg(models.Model):
    """
    用户-组织关联模型
    """
    id = models.AutoField(primary_key=True)  # 自增主键
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE, verbose_name='用户')  # 关联用户
    org = models.ForeignKey(Org, on_delete=models.CASCADE, verbose_name='所属组织')  # 关联组织

    

    class Meta:
        verbose_name = '组织机构用户关系表'
        verbose_name_plural = '组织机构用户关系表'
        unique_together = ('user', 'org')  # 确保用户与组织关系唯一

    def __str__(self):
        """
        返回用户与组织关系的字符串表示
        """
        return f"{self.user.username} - {self.org.name}"



# class ReportIndicator(models.Model):
#     """
#     报告指标模型
#     """
#     name = models.CharField(max_length=255, verbose_name='指标名称')
#     VIP_Kind = models.CharField(
#         max_length=10,
#         choices=[('notvip', '非VIP'), ('vip', 'VIP')],
#         verbose_name='VIP类型'
#     )
#     Kind = models.CharField(max_length=50, verbose_name='指标类型')
#     principle = models.TextField(verbose_name='测量方法')
#     method = models.TextField(verbose_name='指标范围值')
#     interpretation = models.TextField(verbose_name='康复方法')
#     status = models.BooleanField(default=True, verbose_name='状态')
    
    
#     create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
#     create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
#     modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
#     modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

#     deleted_at = models.DateTimeField(null=True, db_index=True)
#     def delete(self):
#         """ 标记为删除而不是物理删除 """
#         self.deleted_at = timezone.now()
#         self.save()

#     class Meta:
#         verbose_name = '报告指标'
#         verbose_name_plural = '报告指标'

#     def __str__(self):
#         return self.name


# class ReportIndicatorRecord(models.Model):
#     """
#     Records of individual report indicator measurements
#     """
#     id = models.AutoField(primary_key=True)
#     name = models.CharField(max_length=255, verbose_name='记录名称')
#     report_indicator_id = models.IntegerField(verbose_name='关联指标ID')
#     name = models.CharField(max_length=255, verbose_name='记录名称')
#     value = models.FloatField(verbose_name='指标值')
#     user_id = models.IntegerField(verbose_name='用户ID')
#     device_id = models.IntegerField(verbose_name='设备ID')
#     user_test_record = models.CharField(max_length=200, verbose_name='关联测试记录')

#     create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
#     create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
#     modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
#     modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

#     deleted_at = models.DateTimeField(null=True, db_index=True)
#     def delete(self):
#         """ 标记为删除而不是物理删除 """
#         self.deleted_at = timezone.now()
#         self.save()

#     class Meta:
#         verbose_name = '报告指标指标记录'
#         verbose_name_plural = '报告指标指标记录'

#     def __str__(self):
#         return f"{self.name} - {self.value}"
    
class ServiceProvider(models.Model):
    """
    服务商模型
    """
    id = models.AutoField(primary_key=True, verbose_name='ID')
    code = models.CharField(max_length=50, unique=True, verbose_name='服务商代码')  # 唯一服务商代码
    name = models.CharField(max_length=100, verbose_name='服务商名称') # 服务商名称
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE, verbose_name='关联用户') # 关联用户

    org  = models.ForeignKey(Org, on_delete=models.CASCADE, verbose_name='所属组织')  # 服务商所属组织
    description = models.TextField(null=True, blank=True, verbose_name='服务商描述')  # 服务商描述
    address = models.CharField(max_length=255, null=True, blank=True, verbose_name='服务商地址')  # 服务商地址
    status = models.BooleanField(default=True, verbose_name='状态')  # 启用/禁用状态


    contact_person = models.CharField(max_length=100, verbose_name='联系人')
    contact_phone = models.CharField(max_length=20, verbose_name='联系电话')
    email = models.EmailField(verbose_name='电子邮箱', null=True, blank=True)
    
    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()



    class Meta:
        verbose_name = '服务商'
        verbose_name_plural = '服务商'

    def __str__(self):
        return self.name

class MiniAppUser(models.Model):
    """
    微信小程序用户模型
    """
    id = models.AutoField(primary_key=True, verbose_name='ID')
    openid = models.CharField(max_length=64, unique=True, verbose_name='微信OpenID')
    unionid = models.CharField(max_length=64, null=True, blank=True, verbose_name='微信UnionID')
    session_key = models.CharField(max_length=128, null=True, blank=True, verbose_name='Session Key')
    test_taker = models.ForeignKey('TestTaker', null=True, blank=True, on_delete=models.SET_NULL, verbose_name='关联体测者')
    
    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modify_user = models.CharField(max_length=50, null=True, blank=True, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')

    deleted_at = models.DateTimeField(null=True, db_index=True)
    def delete(self):
        """ 标记为删除而不是物理删除 """
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name = '微信小程序用户'
        verbose_name_plural = '微信小程序用户'

    def __str__(self):
        return self.openid
    









# class UserTestRecordReport(models.Model):