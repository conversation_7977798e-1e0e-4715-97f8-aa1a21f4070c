from rest_framework import serializers
from .models import Device, Org, UserOrg, DeviceHeartbeat, TestTaker, UserTestRecord, UserTestRecordDetail, UserPhoto, User3DModel, ServiceProvider, MiniAppUser

class DeviceSerializer(serializers.ModelSerializer):
    """
    设备序列化器
    
    用于序列化和反序列化设备数据，包括设备基本信息、状态、位置信息等。
    支持设备的创建、更新、查询等API操作。
    """
    class Meta:
        model = Device
        fields = ('id', 'name', 'online_time', 'offline_time', 'status', 'last_gps', 'last_location', 
                  'device_version', 'org', 'device_type', 'remark', 'create_user', 'create_time', 
                  'modify_user', 'modify_time', 'deleted_at')
        # id: 设备唯一标识
        # name: 设备名称
        # status: 设备状态(online/offline/flash/repair/broken/scrap/maintenance)
        # org: 设备归属组织
        # device_type: 设备类型(aidevice/other)


class OrgSerializer(serializers.ModelSerializer):
    """
    组织机构序列化器
    
    用于序列化和反序列化组织机构数据，包括机构基本信息、类型、层级关系等。
    支持组织机构的创建、更新、查询等API操作。
    """
    class Meta:
        model = Org
        fields = ('id', 'name', 'code', 'kind', 'parent', 'status', 'create_user', 'create_time', 
                  'modify_user', 'modify_time', 'deleted_at')
        # code: 唯一机构代码
        # kind: 机构类型(client/service)
        # parent: 上级机构(自引用)


class DeviceHeartbeatSerializer(serializers.ModelSerializer):
    """
    设备心跳记录序列化器
    
    用于序列化和反序列化设备心跳数据，包括心跳时间、状态、位置、设备运行信息等。
    支持设备心跳记录的创建、查询等API操作，用于监控设备状态。
    """
    class Meta:
        model = DeviceHeartbeat
        fields = ('id', 'device', 'heartbeat_time', 'status', 'gps', 'location', 'cpu_usage', 
                  'memory_usage', 'temperature', 'error_code', 'error_message', 'remark', 
                  'create_user', 'create_time', 'modify_user', 'modify_time', 'deleted_at')
        # status: 心跳状态(online/offline/error)
        # cpu_usage: CPU使用率(%)
        # memory_usage: 内存使用率(%)
        # temperature: 设备温度(℃)


class TestTakerSerializer(serializers.ModelSerializer):
    """
    体测者序列化器
    
    用于序列化和反序列化体测者数据，包括个人基本信息、身体数据、最后测试信息等。
    支持体测者信息的创建、更新、查询等API操作。
    """
    class Meta:
        model = TestTaker
        fields = ('id', 'user', 'name', 'gender', 'age', 'height', 'last_test_date', 
                  'last_test_device', 'last_test_org', 'create_user', 'create_time', 
                  'modify_user', 'modify_time', 'deleted_at')
        # gender: 性别(M/F/O)
        # height: 身高(cm)
        # last_test_date: 最后测试日期
        # last_test_device: 最后测试设备
        # last_test_org: 最后测试机构


class UserTestRecordSerializer(serializers.ModelSerializer):
    """
    测试记录序列化器
    
    用于序列化和反序列化用户测试记录数据，包括测试基本信息、体测者信息、报告状态等。
    支持测试记录的创建、更新、查询等API操作。
    """
    class Meta:
        model = UserTestRecord
        fields = ('id', 'name', 'user', 'device', 'gender', 'age', 'height', 'test_date', 
                  'test_gps', 'report_url', 'report_createtime', 'report_status', 'remark', 
                  'status', 'create_user', 'create_time', 'modify_user', 'modify_time', 'deleted_at')
        # report_status: 报告状态(pending/generating/completed/failed)
        # test_date: 测试时间
        # test_gps: 测试位置(GPS)
        # report_url: 报告URL
        # report_createtime: 报告生成时间


class UserTestRecordDetailSerializer(serializers.ModelSerializer):
    """
    测试记录详情序列化器
    
    用于序列化和反序列化测试记录详情数据，包括详细的测试数据和内容。
    支持测试记录详情的创建、更新、查询等API操作。
    """
    class Meta:
        model = UserTestRecordDetail
        fields = ('id', 'name', 'user_test_record', 'user', 'device', 'content', 'remark', 
                  'create_user', 'create_time', 'modify_user', 'modify_time', 'deleted_at')
        # user_test_record: 关联测试记录
        # content: 检测数据(详细的测试内容)


class UserPhotoSerializer(serializers.ModelSerializer):
    """
    用户照片序列化器
    
    用于序列化和反序列化用户测试照片数据，包括照片URL、拍摄时间等信息。
    支持用户照片的创建、查询等API操作。
    """
    class Meta:
        model = UserPhoto
        fields = ('id', 'name', 'user_test_record', 'photo_index', 'photo_date_time', 
                  'photo_url', 'remark', 'create_user', 'create_time', 'modify_user', 
                  'modify_time', 'deleted_at')
        # photo_index: 照片序号
        # photo_date_time: 拍摄时间
        # photo_url: 照片URL
        # 注意：不包含photo_data字段，因为它是二进制数据，通常不会在API响应中直接返回


class User3DModelSerializer(serializers.ModelSerializer):
    """
    用户3D模型序列化器
    
    用于序列化和反序列化用户3D模型数据，包括模型URL、类型、生成时间等信息。
    支持用户3D模型的创建、查询等API操作。
    """
    class Meta:
        model = User3DModel
        fields = ('id', 'name', 'user_test_record', 'model_type', 'model_url', 
                  'model_date_time', 'create_user', 'create_time', 'modify_user', 
                  'modify_time', 'deleted_at')
        # model_type: 模型格式(obj/stl/fbx/gltf/other)
        # model_url: 模型文件URL
        # model_date_time: 模型生成时间


class UserOrgSerializer(serializers.ModelSerializer):
    """
    用户-组织关联序列化器
    
    用于序列化和反序列化用户与组织的关联数据，建立用户和组织之间的多对多关系。
    支持用户-组织关联的创建、查询等API操作。
    """
    class Meta:
        model = UserOrg
        fields = ('id', 'user', 'org')
        # user: 关联用户
        # org: 关联组织


class ServiceProviderSerializer(serializers.ModelSerializer):
    """
    服务商序列化器
    
    用于序列化和反序列化服务商数据，包括服务商基本信息、联系方式等。
    支持服务商的创建、更新、查询等API操作。
    """
    class Meta:
        model = ServiceProvider
        fields = ('id', 'code', 'name', 'user', 'org', 'description', 'address', 'status',
                  'contact_person', 'contact_phone', 'email', 'create_user', 'create_time', 
                  'modify_user', 'modify_time', 'deleted_at')
        # code: 唯一服务商代码
        # user: 关联用户
        # org: 服务商所属组织
        # contact_person: 联系人
        # contact_phone: 联系电话


class MiniAppUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = MiniAppUser
        fields = ('id', 'openid', 'unionid', 'session_key', 'test_taker', 'create_time', 'modify_time', 'deleted_at')