"""
组织机构API测试模块

测试范围:
1. 组织机构CRUD操作
2. 层级结构管理
3. 用户-组织关联
4. 权限控制
5. 软删除机制
6. 数据验证

作者: AI Assistant
创建时间: 2025-01-04
"""

import json
import pytest
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.utils import timezone
from datetime import datetime, timedelta

# Import JWT tokens - try different import paths for compatibility
try:
    from rest_framework_simplejwt.tokens import RefreshToken
except ImportError:
    try:
        from rest_framework_jwt.tokens import RefreshToken
    except ImportError:
        # Fallback: create a mock RefreshToken for testing
        class RefreshToken:
            def __init__(self, token=None):
                self.access_token = "mock_access_token"

            @classmethod
            def for_user(cls, user):
                return cls()

            def __str__(self):
                return "mock_refresh_token"

from mtts.models import Org, UserOrg
from mtts.serializers import OrgSerializer, UserOrgSerializer


class OrgModelTestCase(TestCase):
    """组织机构模型测试"""

    def setUp(self):
        """测试数据准备"""
        self.parent_org = Org.objects.create(
            name="江苏福瑞健康科技有限公司",
            code="FURUI_ROOT",
            kind="service",
            status=True,
            create_user="admin"
        )

        self.child_org = Org.objects.create(
            name="南京分公司",
            code="FURUI_NJ",
            kind="service",
            parent=self.parent_org,
            status=True,
            create_user="admin"
        )

    def test_org_creation(self):
        """测试组织机构创建"""
        org = Org.objects.create(
            name="测试医院",
            code="TEST_HOSPITAL_001",
            kind="client",
            status=True,
            create_user="test_user"
        )

        self.assertEqual(org.name, "测试医院")
        self.assertEqual(org.code, "TEST_HOSPITAL_001")
        self.assertEqual(org.kind, "client")
        self.assertTrue(org.status)
        self.assertEqual(org.create_user, "test_user")
        self.assertIsNotNone(org.create_time)
        self.assertIsNone(org.deleted_at)

    def test_org_hierarchy(self):
        """测试组织机构层级关系"""
        self.assertEqual(self.child_org.parent, self.parent_org)
        self.assertIsNone(self.parent_org.parent)

    def test_org_str_representation(self):
        """测试字符串表示"""
        self.assertEqual(str(self.parent_org), "江苏福瑞健康科技有限公司")

    def test_org_soft_delete(self):
        """测试软删除功能"""
        org = Org.objects.create(
            name="待删除机构",
            code="TO_DELETE",
            kind="client"
        )

        # 执行软删除
        org.delete()

        # 验证软删除
        org.refresh_from_db()
        self.assertIsNotNone(org.deleted_at)

        # 验证查询过滤
        active_orgs = Org.objects.filter(deleted_at__isnull=True)
        self.assertNotIn(org, active_orgs)

    def test_org_code_uniqueness(self):
        """测试机构代码唯一性"""
        with self.assertRaises(Exception):
            Org.objects.create(
                name="重复代码机构",
                code="FURUI_ROOT",  # 重复的代码
                kind="client"
            )

    def test_org_kind_choices(self):
        """测试机构类型选择"""
        # 测试有效的机构类型
        client_org = Org.objects.create(
            name="客户机构",
            code="CLIENT_001",
            kind="client"
        )
        self.assertEqual(client_org.kind, "client")

        service_org = Org.objects.create(
            name="服务商机构",
            code="SERVICE_001",
            kind="service"
        )
        self.assertEqual(service_org.kind, "service")


class UserOrgModelTestCase(TestCase):
    """用户-组织关联模型测试"""

    def setUp(self):
        """测试数据准备"""
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )

        self.org = Org.objects.create(
            name="测试机构",
            code="TEST_ORG",
            kind="client"
        )

    def test_user_org_creation(self):
        """测试用户-组织关联创建"""
        user_org = UserOrg.objects.create(
            user=self.user,
            org=self.org
        )

        self.assertEqual(user_org.user, self.user)
        self.assertEqual(user_org.org, self.org)

    def test_user_org_str_representation(self):
        """测试字符串表示"""
        user_org = UserOrg.objects.create(
            user=self.user,
            org=self.org
        )

        expected_str = f"{self.user.username} - {self.org.name}"
        self.assertEqual(str(user_org), expected_str)

    def test_user_org_unique_constraint(self):
        """测试用户-组织关联唯一性约束"""
        UserOrg.objects.create(user=self.user, org=self.org)

        # 尝试创建重复关联
        with self.assertRaises(Exception):
            UserOrg.objects.create(user=self.user, org=self.org)


class OrgAPITestCase(APITestCase):
    """组织机构API测试"""

    def setUp(self):
        """测试数据准备"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )

        # 创建管理员用户
        self.admin_user = User.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="adminpass123"
        )

        # 创建测试组织
        self.parent_org = Org.objects.create(
            name="福瑞健康总部",
            code="FURUI_HQ",
            kind="service",
            status=True,
            create_user="admin"
        )

        self.child_org = Org.objects.create(
            name="福瑞健康南京分部",
            code="FURUI_NJ_BRANCH",
            kind="service",
            parent=self.parent_org,
            status=True,
            create_user="admin"
        )

        self.client_org = Org.objects.create(
            name="南京市第一医院",
            code="NJ_HOSPITAL_001",
            kind="client",
            status=True,
            create_user="admin"
        )

        # 创建用户-组织关联
        UserOrg.objects.create(user=self.user, org=self.client_org)

        # 设置API客户端
        self.client = APIClient()

        # 获取JWT令牌
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)

        admin_refresh = RefreshToken.for_user(self.admin_user)
        self.admin_access_token = str(admin_refresh.access_token)

    def test_org_list_authenticated(self):
        """测试认证用户获取组织列表"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')

        url = reverse('org-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 用户只能看到自己所属的组织
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], "南京市第一医院")

    def test_org_list_unauthenticated(self):
        """测试未认证用户访问组织列表"""
        url = reverse('org-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_org_create_success(self):
        """测试成功创建组织"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('org-list')
        data = {
            'name': '新建测试医院',
            'code': 'NEW_TEST_HOSPITAL',
            'kind': 'client',
            'status': True,
            'create_user': 'admin'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], '新建测试医院')
        self.assertEqual(response.data['code'], 'NEW_TEST_HOSPITAL')
        self.assertEqual(response.data['kind'], 'client')

        # 验证数据库中的记录
        org = Org.objects.get(code='NEW_TEST_HOSPITAL')
        self.assertEqual(org.name, '新建测试医院')

    def test_org_create_duplicate_code(self):
        """测试创建重复代码的组织"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('org-list')
        data = {
            'name': '重复代码机构',
            'code': 'FURUI_HQ',  # 重复的代码
            'kind': 'client',
            'status': True
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_org_create_invalid_kind(self):
        """测试创建无效机构类型的组织"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('org-list')
        data = {
            'name': '无效类型机构',
            'code': 'INVALID_KIND_ORG',
            'kind': 'invalid_type',  # 无效的机构类型
            'status': True
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_org_retrieve(self):
        """测试获取单个组织详情"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')

        url = reverse('org-detail', kwargs={'pk': self.client_org.pk})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], "南京市第一医院")
        self.assertEqual(response.data['code'], "NJ_HOSPITAL_001")

    def test_org_update(self):
        """测试更新组织信息"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('org-detail', kwargs={'pk': self.client_org.pk})
        data = {
            'name': '南京市第一医院（更新）',
            'code': 'NJ_HOSPITAL_001',
            'kind': 'client',
            'status': True,
            'modify_user': 'admin'
        }

        response = self.client.put(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], '南京市第一医院（更新）')

        # 验证数据库更新
        org = Org.objects.get(pk=self.client_org.pk)
        self.assertEqual(org.name, '南京市第一医院（更新）')

    def test_org_partial_update(self):
        """测试部分更新组织信息"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('org-detail', kwargs={'pk': self.client_org.pk})
        data = {
            'status': False
        }

        response = self.client.patch(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['status'])

        # 验证数据库更新
        org = Org.objects.get(pk=self.client_org.pk)
        self.assertFalse(org.status)

    def test_org_soft_delete(self):
        """测试组织软删除"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('org-detail', kwargs={'pk': self.client_org.pk})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 验证软删除
        org = Org.objects.get(pk=self.client_org.pk)
        self.assertIsNotNone(org.deleted_at)

        # 验证在活跃列表中不可见
        active_orgs = Org.objects.filter(deleted_at__isnull=True)
        self.assertNotIn(org, active_orgs)


class UserOrgAPITestCase(APITestCase):
    """用户-组织关联API测试"""

    def setUp(self):
        """测试数据准备"""
        # 创建测试用户
        self.user1 = User.objects.create_user(
            username="user1",
            email="<EMAIL>",
            password="testpass123"
        )

        self.user2 = User.objects.create_user(
            username="user2",
            email="<EMAIL>",
            password="testpass123"
        )

        # 创建管理员用户
        self.admin_user = User.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="adminpass123"
        )

        # 创建测试组织
        self.org1 = Org.objects.create(
            name="测试医院1",
            code="TEST_HOSPITAL_1",
            kind="client"
        )

        self.org2 = Org.objects.create(
            name="测试医院2",
            code="TEST_HOSPITAL_2",
            kind="client"
        )

        # 创建用户-组织关联
        self.user_org = UserOrg.objects.create(
            user=self.user1,
            org=self.org1
        )

        # 设置API客户端
        self.client = APIClient()

        # 获取JWT令牌
        admin_refresh = RefreshToken.for_user(self.admin_user)
        self.admin_access_token = str(admin_refresh.access_token)

    def test_user_org_list(self):
        """测试获取用户-组织关联列表"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('userorg-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['user'], self.user1.id)
        self.assertEqual(response.data[0]['org'], self.org1.id)

    def test_user_org_create(self):
        """测试创建用户-组织关联"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('userorg-list')
        data = {
            'user': self.user2.id,
            'org': self.org2.id
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['user'], self.user2.id)
        self.assertEqual(response.data['org'], self.org2.id)

        # 验证数据库中的记录
        user_org = UserOrg.objects.get(user=self.user2, org=self.org2)
        self.assertEqual(user_org.user, self.user2)
        self.assertEqual(user_org.org, self.org2)

    def test_user_org_create_duplicate(self):
        """测试创建重复的用户-组织关联"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('userorg-list')
        data = {
            'user': self.user1.id,
            'org': self.org1.id  # 已存在的关联
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_user_org_delete(self):
        """测试删除用户-组织关联"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_access_token}')

        url = reverse('userorg-detail', kwargs={'pk': self.user_org.pk})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 验证物理删除
        with self.assertRaises(UserOrg.DoesNotExist):
            UserOrg.objects.get(pk=self.user_org.pk)


class OrgSerializerTestCase(TestCase):
    """组织机构序列化器测试"""

    def setUp(self):
        """测试数据准备"""
        self.org_data = {
            'name': '测试序列化器机构',
            'code': 'SERIALIZER_TEST',
            'kind': 'client',
            'status': True
        }

        self.org = Org.objects.create(**self.org_data)

    def test_org_serialization(self):
        """测试组织序列化"""
        serializer = OrgSerializer(self.org)
        data = serializer.data

        self.assertEqual(data['name'], self.org_data['name'])
        self.assertEqual(data['code'], self.org_data['code'])
        self.assertEqual(data['kind'], self.org_data['kind'])
        self.assertEqual(data['status'], self.org_data['status'])
        self.assertIn('id', data)
        self.assertIn('create_time', data)

    def test_org_deserialization_valid(self):
        """测试有效数据反序列化"""
        data = {
            'name': '反序列化测试机构',
            'code': 'DESERIALIZE_TEST',
            'kind': 'service',
            'status': True
        }

        serializer = OrgSerializer(data=data)
        self.assertTrue(serializer.is_valid())

        org = serializer.save()
        self.assertEqual(org.name, data['name'])
        self.assertEqual(org.code, data['code'])
        self.assertEqual(org.kind, data['kind'])

    def test_org_deserialization_invalid(self):
        """测试无效数据反序列化"""
        # 缺少必填字段
        data = {
            'name': '无效数据机构'
            # 缺少 code 和 kind
        }

        serializer = OrgSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('code', serializer.errors)
        self.assertIn('kind', serializer.errors)

    def test_org_update_serialization(self):
        """测试组织更新序列化"""
        update_data = {
            'name': '更新后的机构名称',
            'status': False
        }

        serializer = OrgSerializer(self.org, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())

        updated_org = serializer.save()
        self.assertEqual(updated_org.name, update_data['name'])
        self.assertEqual(updated_org.status, update_data['status'])
        self.assertEqual(updated_org.code, self.org_data['code'])  # 未更新的字段保持不变


class OrgDataTestCase(TestCase):
    """组织机构测试数据集合"""

    @classmethod
    def setUpTestData(cls):
        """创建测试数据集合"""
        # 创建层级组织结构
        cls.create_hierarchical_orgs()

        # 创建不同类型的组织
        cls.create_various_org_types()

        # 创建用户和关联关系
        cls.create_users_and_associations()

    @classmethod
    def create_hierarchical_orgs(cls):
        """创建层级组织结构"""
        # 总部
        cls.headquarters = Org.objects.create(
            name="江苏福瑞健康科技有限公司",
            code="FURUI_HQ",
            kind="service",
            status=True,
            create_user="system"
        )

        # 区域分公司
        cls.east_branch = Org.objects.create(
            name="华东区分公司",
            code="FURUI_EAST",
            kind="service",
            parent=cls.headquarters,
            status=True,
            create_user="system"
        )

        cls.south_branch = Org.objects.create(
            name="华南区分公司",
            code="FURUI_SOUTH",
            kind="service",
            parent=cls.headquarters,
            status=True,
            create_user="system"
        )

        # 城市办事处
        cls.nanjing_office = Org.objects.create(
            name="南京办事处",
            code="FURUI_NJ_OFFICE",
            kind="service",
            parent=cls.east_branch,
            status=True,
            create_user="system"
        )

        cls.shanghai_office = Org.objects.create(
            name="上海办事处",
            code="FURUI_SH_OFFICE",
            kind="service",
            parent=cls.east_branch,
            status=True,
            create_user="system"
        )

        cls.guangzhou_office = Org.objects.create(
            name="广州办事处",
            code="FURUI_GZ_OFFICE",
            kind="service",
            parent=cls.south_branch,
            status=True,
            create_user="system"
        )

    @classmethod
    def create_various_org_types(cls):
        """创建不同类型的客户组织"""
        # 三甲医院
        cls.hospitals = [
            Org.objects.create(
                name="南京市第一医院",
                code="NJ_HOSPITAL_001",
                kind="client",
                status=True,
                create_user="admin"
            ),
            Org.objects.create(
                name="上海交通大学医学院附属瑞金医院",
                code="SH_RUIJIN_HOSPITAL",
                kind="client",
                status=True,
                create_user="admin"
            ),
            Org.objects.create(
                name="广东省人民医院",
                code="GD_PEOPLES_HOSPITAL",
                kind="client",
                status=True,
                create_user="admin"
            )
        ]

        # 体检中心
        cls.health_centers = [
            Org.objects.create(
                name="美年大健康体检中心（南京店）",
                code="MEINIAN_NJ_001",
                kind="client",
                status=True,
                create_user="admin"
            ),
            Org.objects.create(
                name="爱康国宾体检中心（上海店）",
                code="IKANG_SH_001",
                kind="client",
                status=True,
                create_user="admin"
            )
        ]

        # 健身中心
        cls.fitness_centers = [
            Org.objects.create(
                name="威尔士健身（南京新街口店）",
                code="WILLS_NJ_XJK",
                kind="client",
                status=True,
                create_user="admin"
            ),
            Org.objects.create(
                name="一兆韦德健身（上海徐家汇店）",
                code="TERA_SH_XJH",
                kind="client",
                status=True,
                create_user="admin"
            )
        ]

        # 学校
        cls.schools = [
            Org.objects.create(
                name="南京大学医学院",
                code="NJU_MEDICAL_SCHOOL",
                kind="client",
                status=True,
                create_user="admin"
            ),
            Org.objects.create(
                name="上海体育学院",
                code="SUS_SPORTS_UNIVERSITY",
                kind="client",
                status=True,
                create_user="admin"
            )
        ]

        # 已停用的组织
        cls.inactive_org = Org.objects.create(
            name="已停用的测试机构",
            code="INACTIVE_TEST_ORG",
            kind="client",
            status=False,
            create_user="admin"
        )

        # 已删除的组织
        cls.deleted_org = Org.objects.create(
            name="已删除的测试机构",
            code="DELETED_TEST_ORG",
            kind="client",
            status=True,
            create_user="admin"
        )
        cls.deleted_org.delete()  # 软删除

    @classmethod
    def create_users_and_associations(cls):
        """创建用户和组织关联"""
        # 创建不同角色的用户
        cls.admin_user = User.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            first_name="系统",
            last_name="管理员"
        )

        cls.manager_user = User.objects.create_user(
            username="manager",
            email="<EMAIL>",
            password="manager123",
            first_name="区域",
            last_name="经理"
        )

        cls.sales_user = User.objects.create_user(
            username="sales",
            email="<EMAIL>",
            password="sales123",
            first_name="销售",
            last_name="代表"
        )

        cls.hospital_admin = User.objects.create_user(
            username="hospital_admin",
            email="<EMAIL>",
            password="hospital123",
            first_name="医院",
            last_name="管理员"
        )

        cls.doctor_user = User.objects.create_user(
            username="doctor",
            email="<EMAIL>",
            password="doctor123",
            first_name="主治",
            last_name="医师"
        )

        # 创建用户-组织关联
        cls.user_org_associations = [
            UserOrg.objects.create(user=cls.manager_user, org=cls.east_branch),
            UserOrg.objects.create(user=cls.sales_user, org=cls.nanjing_office),
            UserOrg.objects.create(user=cls.hospital_admin, org=cls.hospitals[0]),
            UserOrg.objects.create(user=cls.doctor_user, org=cls.hospitals[0]),
        ]

    def test_hierarchical_structure(self):
        """测试层级结构数据"""
        # 验证总部没有上级
        self.assertIsNone(self.headquarters.parent)

        # 验证分公司的上级是总部
        self.assertEqual(self.east_branch.parent, self.headquarters)
        self.assertEqual(self.south_branch.parent, self.headquarters)

        # 验证办事处的上级是分公司
        self.assertEqual(self.nanjing_office.parent, self.east_branch)
        self.assertEqual(self.shanghai_office.parent, self.east_branch)
        self.assertEqual(self.guangzhou_office.parent, self.south_branch)

    def test_org_types_distribution(self):
        """测试组织类型分布"""
        service_orgs = Org.objects.filter(kind="service", deleted_at__isnull=True)
        client_orgs = Org.objects.filter(kind="client", deleted_at__isnull=True)

        # 验证服务商组织数量（总部+分公司+办事处）
        self.assertEqual(service_orgs.count(), 6)

        # 验证客户组织数量（医院+体检中心+健身中心+学校+停用机构）
        self.assertEqual(client_orgs.count(), 9)

    def test_user_associations(self):
        """测试用户关联数据"""
        # 验证用户-组织关联数量
        self.assertEqual(UserOrg.objects.count(), 4)

        # 验证特定用户的组织关联
        manager_orgs = UserOrg.objects.filter(user=self.manager_user)
        self.assertEqual(manager_orgs.count(), 1)
        self.assertEqual(manager_orgs.first().org, self.east_branch)

        # 验证医院用户关联
        hospital_users = UserOrg.objects.filter(org=self.hospitals[0])
        self.assertEqual(hospital_users.count(), 2)

    def test_soft_delete_data(self):
        """测试软删除数据"""
        # 验证已删除的组织不在活跃列表中
        active_orgs = Org.objects.filter(deleted_at__isnull=True)
        self.assertNotIn(self.deleted_org, active_orgs)

        # 验证已删除的组织仍在数据库中
        all_orgs = Org.objects.all()
        self.assertIn(self.deleted_org, all_orgs)

    def test_status_data(self):
        """测试状态数据"""
        # 验证活跃组织
        active_orgs = Org.objects.filter(status=True, deleted_at__isnull=True)
        self.assertGreater(active_orgs.count(), 0)

        # 验证停用组织
        inactive_orgs = Org.objects.filter(status=False, deleted_at__isnull=True)
        self.assertEqual(inactive_orgs.count(), 1)
        self.assertIn(self.inactive_org, inactive_orgs)


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])