本项目API接口规范分析报告
1. 项目概述
项目名称: MTTS (Medical Testing & Training System) - 江苏福瑞AI服务器
技术栈: Django 5.2.3 + Django REST Framework + MySQL + JWT认证
主要功能: 3D体态检测设备管理、体测数据处理、微信小程序集成

2. API架构设计
2.1 基础架构
框架: Django REST Framework 3.16.0
API版本: v1 (通过URL前缀 /api/v1/ 管理)
路由管理: 使用 DefaultRouter 自动生成RESTful路由
文档系统: 集成 drf-yasg 提供 Swagger UI 和 ReDoc 文档
2.2 认证与权限

settings.py
core/aiserver
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
}
JWT配置:

Access Token 有效期: 1小时
Refresh Token 有效期: 7天
算法: HS256
认证头格式: Bearer <token>
3. API接口分类
3.1 核心业务接口
设备管理 (/api/v1/devices/)

device.py
core/mtts/api
class DeviceViewSet(ModelViewSet):
    """
    设备视图集
    
    提供设备的增删改查功能
    """
    queryset = Device.objects.filter(deleted_at__isnull=True)
    serializer_class = DeviceSerializer
支持操作:

GET /api/v1/devices/ - 获取设备列表
POST /api/v1/devices/ - 创建新设备
GET /api/v1/devices/{id}/ - 获取设备详情
PUT/PATCH /api/v1/devices/{id}/ - 更新设备信息
DELETE /api/v1/devices/{id}/ - 删除设备(软删除)
设备字段:

id: 雪花算法生成的唯一标识
name: 设备名称
status: 设备状态(online/offline/flash/repair/broken/scrap/maintenance)
device_type: 设备类型(aidevice/other)
org: 归属组织
last_gps: GPS位置
device_version: 设备版本
设备心跳监控 (/api/v1/device-heartbeats/)

models.py
core/mtts
class DeviceHeartbeat(models.Model):
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    heartbeat_time = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    cpu_usage = models.FloatField(null=True, blank=True)
    memory_usage = models.FloatField(null=True, blank=True)
    temperature = models.FloatField(null=True, blank=True)
组织机构管理 (/api/v1/orgs/)
支持层级结构的组织管理
机构类型: client(客户) / service(服务商)
用户-组织关联管理 (/api/v1/user-orgs/)
体测用户管理 (/api/v1/user-tests/)

models.py
core/mtts
class TestTaker(models.Model):
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    age = models.PositiveSmallIntegerField()
    height = models.FloatField()
    last_test_date = models.DateField(null=True, blank=True)
体测记录管理 (/api/v1/user-test-records/)
测试基础数据存储
报告生成状态管理
JSON格式的测试内容存储
体测详细数据 (/api/v1/user-test-record-details/)
包含详细的体测指标:

基础测量: 身高、体重、胸围、腰围、臀围
计算数据: 体脂率、BMI、重心分布
形态评估: 头颈形态、肩背形态、骨盆形态等40+项指标
3.2 媒体数据接口
用户照片 (/api/v1/user-photos/)
支持照片URL和二进制数据存储
照片序号管理
与测试记录关联
3D模型文件 (/api/v1/user-3dmodels/)

models.py
core/mtts
class User3DModel(models.Model):
    MODEL_TYPE_CHOICES = [
        ('obj', 'OBJ格式'),
        ('stl', 'STL格式'),
        ('fbx', 'FBX格式'),
        ('gltf', 'glTF格式'),
        ('other', '其他格式')
    ]
3.3 认证接口
JWT认证
POST /api/v1/token/ - 获取JWT令牌
POST /api/v1/token/refresh/ - 刷新令牌
POST /api/v1/token/verify/ - 验证令牌
POST /api/v1/login/ - 自定义登录(返回用户信息)
微信小程序认证

login.py
core/mtts/api
class MiniAppWeChatLoginView(APIView):
    """
    微信小程序登录/注册视图
    前端传 code，后端用 code 换 openid，自动注册并下发 JWT Token
    """
    permission_classes = [AllowAny]
POST /api/miniapp/login/ - 微信小程序登录
POST /api/miniapp/register/ - 小程序用户注册
POST /api/miniapp/test/submit/ - 体测数据提交
GET /api/miniapp/report/ - 体测报告查询
3.4 模拟测试接口
数据模拟
POST /api/mock/receive - 模拟设备端数据接收
GET /api/mock/send - 模拟后端向小程序发送数据
模拟数据格式:


mock_receive.json
core/mtts/json
{
    "device_id": "DEVICE_1",
    "timestamp": "2025-07-25T14:03:00Z",
    "openid": "o_BGkvsfOCsybswo_47dGui1IBT6",
    "json_content": {
        "gender": "M",
        "height": 174.0,
        "weight": 76.8,
        "age": 35,
        "chest": 102.5,

4. 响应格式规范
4.1 统一响应格式

response.py
core/mtts/utils
class APIResponse(Response):
    def __init__(self, data_status=0, data_msg='ok', results=None, http_status=None, headers=None, exception=False,
                 **kwargs):
        data = {
            'status': data_status,
            'msg': data_msg,
        }
        if results is not None:
            data['results'] = results
        data.update(kwargs)
标准响应结构:

{
    "status": 0,
    "msg": "ok",
    "results": {...}
}
4.2 错误处理
status: 0 - 成功
status: 1 - 业务错误
HTTP状态码遵循RESTful规范
5. 数据模型特点
5.1 软删除机制
所有核心模型都实现软删除:


models.py
core/mtts
deleted_at = models.DateTimeField(null=True, db_index=True)
def delete(self):
    """ 标记为删除而不是物理删除 """
    self.deleted_at = timezone.now()
    self.save()
5.2 雪花算法ID生成

snowflake.py
core/mtts/utils
class SnowflakeGenerator:
    """
    雪花ID生成器
    结构：1位符号位 + 41位时间戳 + 10位工作机器ID + 12位序列号
    """
5.3 审计字段
所有模型包含标准审计字段:

create_user: 创建人
create_time: 创建时间
modify_user: 修改人
modify_time: 修改时间
6. API文档访问
Swagger UI: /swagger/
ReDoc: /redoc/
管理后台: /admin/
API Schema: /swagger.json 或 /swagger.yaml
7. 部署配置
7.1 数据库
类型: MySQL
连接: 阿里云RDS
字符集: UTF-8
7.2 服务器配置

gunicorn_conf.py
core
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = 'uvicorn.workers.UvicornWorker'
bind = '0.0.0.0:8000'
8. 安全特性
JWT认证: 支持访问令牌和刷新令牌
权限控制: 默认需要认证，部分接口允许匿名访问
CSRF保护: Django内置CSRF中间件
软删除: 数据安全，支持恢复
参数验证: DRF序列化器自动验证
9. 扩展性设计
模块化架构: API按功能模块分离
版本控制: URL路径版本管理
中间件支持: 可扩展认证和权限中间件
序列化器: 灵活的数据序列化和验证
ViewSet: 标准化的CRUD操作
这个API接口规范体现了现代Web API的最佳实践，具有良好的可维护性、扩展性和安全性。